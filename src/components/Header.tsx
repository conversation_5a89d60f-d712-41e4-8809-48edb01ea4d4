import { Link, useLocation } from 'react-router-dom'
import { User, Menu } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useState } from 'react'
import Logo from './Logo'

interface HeaderProps {
  onSidebarToggle?: () => void
  isDashboard?: boolean
}

export default function Header({ onSidebarToggle, isDashboard }: HeaderProps) {
  const location = useLocation()
  const { user, signOut } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const isActive = (path: string) => location.pathname === path

  // Determine if we're on a dashboard page
  const isOnDashboard = isDashboard || location.pathname.startsWith('/dashboard')

  return (
    <header className="bg-background shadow-sm border-b border-border">
      <div className={isOnDashboard ? "px-4 lg:px-6" : "container mx-auto px-6"}>
        <div className="flex items-center justify-between h-16">
          {/* Left side - Logo or Dashboard controls */}
          {isOnDashboard ? (
            <>
              {/* Mobile sidebar toggle for dashboard */}
              <button
                onClick={onSidebarToggle}
                className="p-2 min-h-[44px] min-w-[44px] rounded-md text-muted-foreground hover:text-foreground lg:hidden flex items-center justify-center"
                aria-label="Open sidebar navigation"
              >
                <Menu className="w-6 h-6" aria-hidden="true" />
              </button>

              {/* Dashboard title */}
              <div className="flex-1 flex justify-center lg:justify-start lg:ml-0">
                <h1 className="text-xl font-semibold text-foreground">
                  Mission Fresh Dashboard
                </h1>
              </div>
            </>
          ) : (
            <>
              {/* Logo for public pages */}
              <Logo size="md" showText={true} linkTo="/" />

              {/* Desktop Navigation for public pages */}
              <nav className="hidden lg:flex items-center space-x-6">
                <Link
                  to="/"
                  className={`nav-link min-h-[44px] px-4 py-2 flex items-center font-medium tracking-tight transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${isActive('/') ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}`}
                >
                  Home
                </Link>
                <Link
                  to="/how-it-works"
                  className={`nav-link min-h-[44px] px-4 py-2 flex items-center font-medium tracking-tight transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${isActive('/how-it-works') ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}`}
                >
                  How It Works
                </Link>
                <Link
                  to="/features"
                  className={`nav-link min-h-[44px] px-4 py-2 flex items-center font-medium tracking-tight transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${isActive('/features') ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}`}
                >
                  Features
                </Link>
                <Link
                  to="/tools"
                  className={`nav-link min-h-[44px] px-4 py-2 flex items-center font-medium tracking-tight transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${isActive('/tools') ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}`}
                >
                  Tools
                </Link>
                <Link
                  to="/fresh-assistant"
                  className={`nav-link min-h-[44px] px-4 py-2 flex items-center font-medium tracking-tight transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${isActive('/fresh-assistant') ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}`}
                >
                  AI Assistant
                </Link>
                <Link
                  to="/search"
                  className={`nav-link min-h-[44px] px-4 py-2 flex items-center font-medium tracking-tight transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${isActive('/search') ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}`}
                >
                  Search
                </Link>
              </nav>

              {/* Mobile Menu Button for public pages */}
              <button
                className="lg:hidden p-2 text-muted-foreground hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                aria-label={isMenuOpen ? 'Close Menu' : 'Open Menu'}
                aria-expanded={isMenuOpen}
              >
                {isMenuOpen ? (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <Menu className="w-6 h-6" />
                )}
              </button>
            </>
          )}

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden p-2 text-muted-foreground hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? 'Close Menu' : 'Open Menu'}
            aria-expanded={isMenuOpen}
          >
            {isMenuOpen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>

          {/* User Menu */}
          <div className="flex items-center space-x-3">
            {user ? (
              <>
                <Link 
                  to="/dashboard" 
                  className="btn-primary px-4 py-2 min-h-[44px] flex items-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                >
                  Dashboard
                </Link>
                <div className="flex items-center gap-2 bg-muted/50 rounded-lg px-3 py-2 min-h-[44px]">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-primary-foreground" />
                  </div>
                  <span className="text-sm font-medium text-muted-foreground truncate max-w-[120px]">
                    {user.user_metadata?.display_name || user.email?.split('@')[0] || 'User'}
                  </span>
                  <button
                    onClick={signOut}
                    className="text-muted-foreground hover:text-destructive p-1 rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-destructive focus:ring-offset-2"
                    aria-label="Sign Out"
                    title="Sign Out"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </div>
              </>
            ) : (
              <>
                <Link 
                  to="/auth?mode=signin" 
                  className="btn-secondary px-4 py-2 min-h-[44px] flex items-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                >
                  Log In
                </Link>
                <Link 
                  to="/auth?mode=signup" 
                  className="btn-primary px-4 py-2 min-h-[44px] flex items-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                >
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* Mobile Menu Dropdown */}
      {isMenuOpen && (
        <div className="lg:hidden bg-background border-b border-border shadow-lg">
          <div className="container mx-auto px-6 py-4">
            <nav className="flex flex-col space-y-4">
              <Link 
                to="/" 
                className={`p-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                  isActive('/') 
                    ? 'text-primary bg-primary/10' 
                    : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              <Link 
                to="/how-it-works" 
                className={`p-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                  isActive('/how-it-works') 
                    ? 'text-primary bg-primary/10' 
                    : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                How It Works
              </Link>
              <Link 
                to="/features" 
                className={`p-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                  isActive('/features') 
                    ? 'text-primary bg-primary/10' 
                    : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Features
              </Link>
              <Link 
                to="/tools" 
                className={`p-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                  isActive('/tools') 
                    ? 'text-primary bg-primary/10' 
                    : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Tools
              </Link>
              <Link 
                to="/ai-assistant" 
                className={`p-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                  isActive('/ai-assistant') 
                    ? 'text-primary bg-primary/10' 
                    : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                AI Assistant
              </Link>
              <Link 
                to="/search" 
                className={`p-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                  isActive('/search') 
                    ? 'text-primary bg-primary/10' 
                    : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Search
              </Link>
              
              {/* Mobile User Actions */}
              <div className="pt-4 border-t border-border">
                {user ? (
                  <>
                    <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg mb-4">
                      <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-primary-foreground" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-foreground">
                          {user.user_metadata?.display_name || user.email?.split('@')[0] || 'User'}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </div>
                    <Link 
                      to="/dashboard" 
                      className="btn-primary w-full justify-center mb-3 px-4 py-3 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <button
                      onClick={() => {
                        signOut()
                        setIsMenuOpen(false)
                      }}
                      className="w-full text-left p-3 text-muted-foreground hover:text-destructive hover:bg-destructive/5 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-destructive focus:ring-offset-2"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <Link 
                      to="/auth?mode=signin" 
                      className="btn-secondary w-full justify-center mb-3 px-4 py-3 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Log In
                    </Link>
                    <Link 
                      to="/auth?mode=signup" 
                      className="btn-primary w-full justify-center px-4 py-3 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Sign Up
                    </Link>
                  </>
                )}
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  )
}
