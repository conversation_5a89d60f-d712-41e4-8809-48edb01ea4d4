{"supabase": {"command": "/opt/homebrew/bin/node", "args": ["/Users/<USER>/.npm-global/lib/node_modules/@supabase/mcp-server-supabase/dist/transports/stdio.js"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************", "SUPABASE_URL": "https://yekarqanirdkdckimpna.supabase.co", "SUPABASE_PROJECT_REF": "yekarqanirdkdckimpna", "HTTP_PROXY": "http://127.0.0.1:9910", "HTTPS_PROXY": "http://127.0.0.1:9910", "NO_PROXY": "localhost,127.0.0.1,*.supabase.co,supabase.com,yekarqanirdkdckimpna.supabase.co"}, "disabled": false, "alwaysAllow": ["rebase_branch", "reset_branch", "merge_branch", "delete_branch", "list_branches", "create_branch", "generate_typescript_types", "get_anon_key", "get_project_url", "get_logs", "deploy_edge_function", "list_edge_functions", "list_migrations", "list_extensions", "restore_project", "pause_project", "create_project", "confirm_cost", "get_cost", "get_project", "list_projects", "get_organization", "list_organizations", "access_mcp_resource", "create_sql_migration", "apply_migrations", "get_table_schema", "create_migration", "list_functions", "search_docs", "create_migration_file", "query", "get_table_definition", "get_function_definition", "query_sql", "apply_migration", "get_advisors", "list_tables", "execute_sql"], "disabledTools": []}}