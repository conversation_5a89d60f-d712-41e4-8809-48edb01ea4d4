{"puppeteer": {"command": "/opt/homebrew/bin/node", "args": ["/Users/<USER>/.npm-global/lib/node_modules/@modelcontextprotocol/server-puppeteer/dist/index.js"], "env": {"HTTP_PROXY": "http://127.0.0.1:9910", "HTTPS_PROXY": "http://127.0.0.1:9910", "NO_PROXY": "localhost,127.0.0.1,*.local"}, "disabled": false, "alwaysAllow": ["puppeteer_navigate", "puppeteer_screenshot", "puppeteer_click", "puppeteer_fill", "puppeteer_select", "puppeteer_hover", "puppeteer_evaluate"], "disabledTools": []}}